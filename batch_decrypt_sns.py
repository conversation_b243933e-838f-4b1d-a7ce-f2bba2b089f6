import os
from PIL import Image

def analyze_file_format(file_path):
    """分析文件格式和XOR密钥"""
    try:
        with open(file_path, 'rb') as f:
            header = f.read(10)

        # 常见图片格式签名
        signatures = {
            'JPEG': bytes.fromhex('ffd8ffe0'),
            'PNG': bytes.fromhex('89504e47'),
            'GIF': bytes.fromhex('47494638'),
            'BMP': bytes.fromhex('424d'),
            'WEBP': bytes.fromhex('52494646')
        }

        # 首先尝试常见的朋友圈XOR密钥
        common_keys = [0x45, 0x00, 0xE7, 0x42, 0x89, 0xFF]

        for xor_key in common_keys:
            for format_name, sig in signatures.items():
                if len(header) >= len(sig):
                    # 验证密钥一致性
                    match = True
                    for i in range(min(len(sig), len(header))):
                        if (header[i] ^ xor_key) != sig[i]:
                            match = False
                            break

                    if match:
                        return format_name.lower(), xor_key

        # 如果常见密钥都不匹配，尝试自动检测
        for format_name, sig in signatures.items():
            if len(header) >= len(sig):
                xor_key = header[0] ^ sig[0]

                # 验证密钥一致性
                match = True
                for i in range(min(len(sig), len(header))):
                    if (header[i] ^ xor_key) != sig[i]:
                        match = False
                        break

                if match:
                    return format_name.lower(), xor_key

        return None, None
    except:
        return None, None

def decrypt_file(input_path, output_path, xor_key):
    """解密单个文件"""
    try:
        with open(input_path, 'rb') as infile, open(output_path, 'wb') as outfile:
            while True:
                chunk = infile.read(8192)
                if not chunk:
                    break
                decrypted = bytes([b ^ xor_key for b in chunk])
                outfile.write(decrypted)
        return True
    except Exception as e:
        print(f"解密失败 {input_path}: {e}")
        return False

def verify_image(file_path):
    """验证图片是否可以正常打开"""
    try:
        # 检查文件大小是否合理
        if os.path.getsize(file_path) < 100:
            return False

        # 尝试打开图片
        with Image.open(file_path) as img:
            img.verify()
        return True
    except Exception as e:
        # 对于BMP等格式，即使验证失败也可能是有效的
        # 检查文件头是否正确
        try:
            with open(file_path, 'rb') as f:
                header = f.read(4)
                # 检查是否为有效的图片文件头
                if header.startswith(b'\xff\xd8\xff') or header.startswith(b'\x89PNG') or header.startswith(b'BM'):
                    return True
        except:
            pass
        return False

def batch_decrypt_sns_folder(source_folder, target_folder):
    """批量解密朋友圈文件夹"""
    if not os.path.exists(target_folder):
        os.makedirs(target_folder)
    
    success_count = 0
    total_count = 0
    
    # 遍历所有子文件夹
    for root, dirs, files in os.walk(source_folder):
        for file in files:
            file_path = os.path.join(root, file)
            
            # 跳过已有扩展名的文件
            if '.' in file:
                continue
                
            total_count += 1
            print(f"处理文件 {total_count}: {file}")
            
            # 分析文件格式
            format_type, xor_key = analyze_file_format(file_path)
            
            if format_type and xor_key is not None:
                # 构建输出文件路径
                relative_path = os.path.relpath(file_path, source_folder)
                output_dir = os.path.join(target_folder, os.path.dirname(relative_path))
                if not os.path.exists(output_dir):
                    os.makedirs(output_dir)
                
                output_file = os.path.join(output_dir, f"{file}.{format_type}")
                
                # 解密文件
                if decrypt_file(file_path, output_file, xor_key):
                    # 验证图片
                    if verify_image(output_file):
                        print(f"✓ 成功: {format_type.upper()}, XOR: 0x{xor_key:02x}")
                        success_count += 1
                    else:
                        print(f"✗ 解密后图片损坏")
                        os.remove(output_file)  # 删除损坏的文件
                else:
                    print(f"✗ 解密失败")
            else:
                print(f"✗ 无法识别格式")
    
    print(f"\n解密完成！成功: {success_count}/{total_count}")
    return success_count

# 执行批量解密
source_folder = r"C:\Users\<USER>\xwechat_files\wxid_r5gn051wxezm22_62c6\cache\2025-08\Sns\Img"
target_folder = r"C:\Users\<USER>\Desktop\解密的朋友圈图片"

print("开始批量解密朋友圈图片...")
success_count = batch_decrypt_sns_folder(source_folder, target_folder)

if success_count > 0:
    print(f"解密成功！共解密 {success_count} 张图片")
    print(f"文件保存在: {target_folder}")
else:
    print("没有成功解密任何文件，请检查文件路径")