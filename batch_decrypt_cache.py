import os
from PIL import Image

def analyze_file_format(file_path):
    """分析文件格式和XOR密钥"""
    try:
        with open(file_path, 'rb') as f:
            header = f.read(20)  # 读取更多字节以支持更多格式
        
        # 扩展的文件格式签名
        signatures = {
            # 图片格式
            'JPEG': bytes.fromhex('ffd8ffe0'),
            'PNG': bytes.fromhex('89504e47'), 
            'GIF': bytes.fromhex('47494638'),
            'BMP': bytes.fromhex('424d'),
            'WEBP': bytes.fromhex('52494646'),
            'TIFF': bytes.fromhex('49492a00'),
            # 视频格式
            'MP4': bytes.fromhex('00000020667479704d534e56'),  # MP4
            'AVI': bytes.fromhex('52494646'),  # AVI (RIFF)
            'MOV': bytes.fromhex('6d6f6f76'),  # MOV
            'FLV': bytes.fromhex('464c5601'),  # FLV
            # 文档格式
            'PDF': bytes.fromhex('255044462d'),
            'DOC': bytes.fromhex('d0cf11e0a1b11ae1'),
            # 音频格式
            'MP3': bytes.fromhex('494433'),  # ID3
            'WAV': bytes.fromhex('52494646'),  # RIFF WAV
        }
        
        for format_name, sig in signatures.items():
            if len(header) >= len(sig):
                xor_key = header[0] ^ sig[0]
                
                # 验证密钥一致性
                match = True
                for i in range(min(len(sig), len(header))):
                    if (header[i] ^ xor_key) != sig[i]:
                        match = False
                        break
                
                if match:
                    return format_name.lower(), xor_key
        
        return None, None
    except:
        return None, None

def decrypt_file(input_path, output_path, xor_key):
    """解密单个文件"""
    try:
        with open(input_path, 'rb') as infile, open(output_path, 'wb') as outfile:
            while True:
                chunk = infile.read(65536)  # 64KB块读取，提高效率
                if not chunk:
                    break
                decrypted = bytes([b ^ xor_key for b in chunk])
                outfile.write(decrypted)
        return True
    except Exception as e:
        print(f"解密失败 {input_path}: {e}")
        return False

def verify_file(file_path, file_type):
    """验证文件是否可以正常打开"""
    try:
        if file_type.lower() in ['jpeg', 'jpg', 'png', 'gif', 'bmp', 'webp', 'tiff']:
            with Image.open(file_path) as img:
                img.verify()
            return True
        elif file_type.lower() in ['pdf', 'doc', 'mp4', 'avi', 'mp3', 'wav']:
            # 对于其他格式，检查文件大小是否合理
            return os.path.getsize(file_path) > 100
        else:
            return True
    except:
        return False

def batch_decrypt_cache_folder(source_folder, target_folder):
    """批量解密缓存文件夹"""
    if not os.path.exists(target_folder):
        os.makedirs(target_folder)
    
    success_count = 0
    total_count = 0
    file_types = {}
    
    print(f"开始扫描文件夹: {source_folder}")
    
    # 遍历所有子文件夹和文件
    for root, dirs, files in os.walk(source_folder):
        for file in files:
            file_path = os.path.join(root, file)
            
            # 跳过已有扩展名的文件（可能是系统文件）
            if '.' in file and len(file.split('.')[-1]) <= 4:
                continue
                
            total_count += 1
            print(f"处理文件 {total_count}: {os.path.basename(file)}")
            
            # 分析文件格式
            format_type, xor_key = analyze_file_format(file_path)
            
            if format_type and xor_key is not None:
                # 构建输出文件路径，保持目录结构
                relative_path = os.path.relpath(file_path, source_folder)
                output_dir = os.path.join(target_folder, os.path.dirname(relative_path))
                if not os.path.exists(output_dir):
                    os.makedirs(output_dir)
                
                output_file = os.path.join(output_dir, f"{file}.{format_type}")
                
                # 解密文件
                if decrypt_file(file_path, output_file, xor_key):
                    # 验证文件
                    if verify_file(output_file, format_type):
                        print(f"✓ 成功: {format_type.upper()}, XOR: 0x{xor_key:02x}, 大小: {os.path.getsize(file_path)/1024:.1f}KB")
                        success_count += 1
                        
                        # 统计文件类型
                        if format_type in file_types:
                            file_types[format_type] += 1
                        else:
                            file_types[format_type] = 1
                    else:
                        print(f"✗ 解密后文件损坏")
                        os.remove(output_file)  # 删除损坏的文件
                else:
                    print(f"✗ 解密失败")
            else:
                print(f"✗ 无法识别格式")
    
    print(f"\n解密完成！成功: {success_count}/{total_count}")
    
    if file_types:
        print("\n文件类型统计:")
        for file_type, count in file_types.items():
            print(f"  {file_type.upper()}: {count} 个")
    
    return success_count

# 执行批量解密
source_folder = r"C:\Users\<USER>\xwechat_files\wxid_r5gn051wxezm22_62c6\cache\2025-07"
target_folder = r"C:\Users\<USER>\Desktop\解密的2025-07缓存"

print("开始批量解密2025-07缓存文件夹...")
success_count = batch_decrypt_cache_folder(source_folder, target_folder)

if success_count > 0:
    print(f"\n解密成功！共解密 {success_count} 个文件")
    print(f"文件保存在: {target_folder}")
    print("\n建议检查解密后的文件，确认内容正确性")
else:
    print("没有成功解密任何文件，请检查文件路径")