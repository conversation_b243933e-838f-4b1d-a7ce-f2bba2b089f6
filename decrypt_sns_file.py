def decrypt_wechat_sns_file(input_path, output_path, xor_key):
    """解密微信朋友圈文件"""
    try:
        with open(input_path, 'rb') as infile, open(output_path, 'wb') as outfile:
            while True:
                chunk = infile.read(8192)  # 8KB块读取
                if not chunk:
                    break
                
                # XOR解密
                decrypted = bytes([b ^ xor_key for b in chunk])
                outfile.write(decrypted)
        
        print(f"解密完成: {output_path}")
        return True
        
    except Exception as e:
        print(f"解密失败: {e}")
        return False

# 解密你的朋友圈图片
input_file = r"C:\Users\<USER>\xwechat_files\wxid_r5gn051wxezm22_62c6\cache\2025-08\Sns\Img\f3\935f32cb41402c3f78b4252c70eaa9"
output_file = r"C:\Users\<USER>\Desktop\decrypted_sns_image.bmp"

success = decrypt_wechat_sns_file(input_file, output_file, 0x45)

if success:
    print("朋友圈图片解密成功！")
    print(f"解密后的文件保存在: {output_file}")
else:
    print("解密失败，请检查文件路径和权限")