import os

def analyze_wechat_file(file_path):
    """分析微信缓存文件"""
    if not os.path.exists(file_path):
        print("文件不存在")
        return
    
    # 读取文件头部
    with open(file_path, 'rb') as f:
        header = f.read(20)  # 读取前20字节
    
    print(f"文件大小: {os.path.getsize(file_path)} 字节")
    print("文件头部十六进制:")
    hex_str = ' '.join([f'{b:02x}' for b in header])
    print(hex_str)
    
    # 检查是否为常见图片格式的加密版本
    check_encrypted_format(header)

def check_encrypted_format(header):
    """检查可能的加密格式"""
    # 常见图片格式的标准头部
    signatures = {
        'JPEG': bytes.fromhex('ffd8ffe0'),
        'PNG': bytes.fromhex('89504e47'),
        'GIF': bytes.fromhex('47494638'),
        'BMP': bytes.fromhex('424d'),
        'WEBP': bytes.fromhex('52494646')
    }
    
    print("\n尝试检测XOR密钥:")
    for format_name, sig in signatures.items():
        if len(header) >= len(sig):
            # 计算可能的XOR密钥
            xor_key = header[0] ^ sig[0]
            
            # 验证密钥是否一致
            match = True
            for i in range(min(len(sig), len(header))):
                if (header[i] ^ xor_key) != sig[i]:
                    match = False
                    break
            
            if match:
                print(f"可能是{format_name}格式，XOR密钥: 0x{xor_key:02x}")
                return format_name.lower(), xor_key
    
    print("未检测到标准图片格式")
    return None, None

# 分析你的文件
file_path = r"C:\Users\<USER>\xwechat_files\wxid_r5gn051wxezm22_62c6\cache\2025-08\Sns\Img\f3\935f32cb41402c3f78b4252c70eaa9"
analyze_wechat_file(file_path)