import os
from PIL import Image

def analyze_file_format(file_path):
    """分析文件格式和XOR密钥"""
    try:
        with open(file_path, 'rb') as f:
            header = f.read(20)
        
        # 常见图片和视频格式签名
        signatures = {
            'JPEG': bytes.fromhex('ffd8ffe0'),
            'PNG': bytes.fromhex('89504e47'), 
            'GIF': bytes.fromhex('47494638'),
            'BMP': bytes.fromhex('424d'),
            'WEBP': bytes.fromhex('52494646'),
            'MP4': bytes.fromhex('00000020667479704d534e56'),
            'AVI': bytes.fromhex('52494646'),
            'MOV': bytes.fromhex('6d6f6f76')
        }
        
        # 朋友圈常见的XOR密钥
        common_keys = [0x45, 0x00, 0xE7, 0x42, 0x89, 0xFF, 0x23, 0x67]
        
        for xor_key in common_keys:
            for format_name, sig in signatures.items():
                if len(header) >= len(sig):
                    # 验证密钥一致性
                    match = True
                    for i in range(min(len(sig), len(header))):
                        if (header[i] ^ xor_key) != sig[i]:
                            match = False
                            break
                    
                    if match:
                        return format_name.lower(), xor_key
        
        # 如果常见密钥都不匹配，尝试自动检测
        for format_name, sig in signatures.items():
            if len(header) >= len(sig):
                xor_key = header[0] ^ sig[0]
                
                # 验证密钥一致性
                match = True
                for i in range(min(len(sig), len(header))):
                    if (header[i] ^ xor_key) != sig[i]:
                        match = False
                        break
                
                if match:
                    return format_name.lower(), xor_key
        
        return None, None
    except:
        return None, None

def decrypt_file(input_path, output_path, xor_key):
    """解密单个文件"""
    try:
        with open(input_path, 'rb') as infile, open(output_path, 'wb') as outfile:
            while True:
                chunk = infile.read(65536)  # 64KB块读取
                if not chunk:
                    break
                decrypted = bytes([b ^ xor_key for b in chunk])
                outfile.write(decrypted)
        return True
    except Exception as e:
        print(f"解密失败 {input_path}: {e}")
        return False

def verify_file(file_path, file_type):
    """验证文件是否有效"""
    try:
        # 检查文件大小是否合理
        if os.path.getsize(file_path) < 100:
            return False
            
        # 对于图片文件，尝试验证
        if file_type.lower() in ['jpeg', 'jpg', 'png', 'gif', 'webp']:
            try:
                with Image.open(file_path) as img:
                    img.verify()
                return True
            except:
                pass
        
        # 检查文件头是否正确
        with open(file_path, 'rb') as f:
            header = f.read(8)
            # 检查是否为有效的文件头
            if (header.startswith(b'\xff\xd8\xff') or  # JPEG
                header.startswith(b'\x89PNG') or       # PNG
                header.startswith(b'BM') or            # BMP
                header.startswith(b'GIF8') or          # GIF
                header.startswith(b'RIFF') or          # WEBP/AVI
                header.startswith(b'\x00\x00\x00\x20ftypmp4') or  # MP4
                header.startswith(b'moov')):           # MOV
                return True
        
        return False
    except:
        return False

def decrypt_sns_directory(source_folder, target_folder):
    """解密朋友圈目录"""
    if not os.path.exists(target_folder):
        os.makedirs(target_folder)
    
    success_count = 0
    total_count = 0
    file_types = {}
    
    print(f"开始扫描朋友圈目录: {source_folder}")
    
    # 遍历所有子文件夹
    for root, dirs, files in os.walk(source_folder):
        for file in files:
            file_path = os.path.join(root, file)
            
            # 跳过已有扩展名的文件
            if '.' in file and len(file.split('.')[-1]) <= 4:
                continue
                
            total_count += 1
            print(f"处理文件 {total_count}: {os.path.basename(file)}")
            
            # 分析文件格式
            format_type, xor_key = analyze_file_format(file_path)
            
            if format_type and xor_key is not None:
                # 构建输出文件路径，保持目录结构
                relative_path = os.path.relpath(file_path, source_folder)
                output_dir = os.path.join(target_folder, os.path.dirname(relative_path))
                if not os.path.exists(output_dir):
                    os.makedirs(output_dir)
                
                output_file = os.path.join(output_dir, f"{file}.{format_type}")
                
                # 解密文件
                if decrypt_file(file_path, output_file, xor_key):
                    # 验证文件
                    if verify_file(output_file, format_type):
                        file_size = os.path.getsize(file_path) / 1024
                        print(f"✓ 成功: {format_type.upper()}, XOR: 0x{xor_key:02x}, 大小: {file_size:.1f}KB")
                        success_count += 1
                        
                        # 统计文件类型
                        if format_type in file_types:
                            file_types[format_type] += 1
                        else:
                            file_types[format_type] = 1
                    else:
                        print(f"✗ 解密后文件损坏")
                        os.remove(output_file)  # 删除损坏的文件
                else:
                    print(f"✗ 解密失败")
            else:
                print(f"✗ 无法识别格式")
    
    print(f"\n解密完成！成功: {success_count}/{total_count}")
    
    if file_types:
        print("\n文件类型统计:")
        for file_type, count in file_types.items():
            print(f"  {file_type.upper()}: {count} 个")
    
    return success_count

def main():
    """主函数 - 解密多个月份的朋友圈内容"""
    base_path = r"C:\Users\<USER>\xwechat_files\wxid_r5gn051wxezm22_62c6\cache"
    target_base = r"C:\Users\<USER>\Desktop\解密的朋友圈内容"
    
    # 要处理的月份
    months = ["2025-07", "2025-08"]
    
    total_success = 0
    
    for month in months:
        print(f"\n{'='*50}")
        print(f"开始处理 {month} 的朋友圈内容")
        print(f"{'='*50}")
        
        # 处理图片
        img_source = os.path.join(base_path, month, "Sns", "Img")
        img_target = os.path.join(target_base, month, "图片")
        
        if os.path.exists(img_source):
            print(f"\n处理朋友圈图片: {img_source}")
            success = decrypt_sns_directory(img_source, img_target)
            total_success += success
        else:
            print(f"未找到图片目录: {img_source}")
        
        # 处理视频
        video_source = os.path.join(base_path, month, "Sns", "Video")
        video_target = os.path.join(target_base, month, "视频")
        
        if os.path.exists(video_source):
            print(f"\n处理朋友圈视频: {video_source}")
            success = decrypt_sns_directory(video_source, video_target)
            total_success += success
        else:
            print(f"未找到视频目录: {video_source}")
    
    print(f"\n{'='*50}")
    print(f"全部解密完成！总共成功解密 {total_success} 个文件")
    print(f"文件保存在: {target_base}")
    print(f"{'='*50}")

if __name__ == "__main__":
    main()
